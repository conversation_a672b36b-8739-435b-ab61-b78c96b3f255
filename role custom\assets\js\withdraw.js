/**
 * Role Custom Para Çekme JavaScript
 */

jQuery(document).ready(function($) {
    'use strict';

    // Modal HTML'ini sayfaya ekle
    const modalHTML = `
        <div id="account-modal" class="role-custom-modal">
            <div class="role-custom-modal-content">
                <div class="role-custom-modal-header">
                    <h3>Hesap <PERSON></h3>
                    <button type="button" class="role-custom-modal-close">&times;</button>
                </div>
                <div class="role-custom-modal-body">
                    <form id="account-form">
                        <div class="form-group">
                            <label for="bank-name">Banka Adı *</label>
                            <input type="text" id="bank-name" name="bank_name" class="regular-text" required />
                        </div>
                        <div class="form-group">
                            <label for="account-holder">Hesap Sahibi Adı *</label>
                            <input type="text" id="account-holder" name="account_holder" class="regular-text" required />
                        </div>
                        <div class="form-group">
                            <label for="iban">IBAN *</label>
                            <input type="text" id="iban" name="iban" class="regular-text" required 
                                   placeholder="TR00 0000 0000 0000 0000 0000 00" />
                        </div>
                        <div class="form-group">
                            <label for="account-number">Hesap Numarası</label>
                            <input type="text" id="account-number" name="account_number" class="regular-text" />
                        </div>
                    </form>
                </div>
                <div class="role-custom-modal-footer">
                    <button type="button" class="button" id="cancel-account">İptal</button>
                    <button type="button" class="button button-primary" id="save-account">Kaydet</button>
                </div>
            </div>
        </div>
    `;
    
    $('body').append(modalHTML);

    // Modal açma/kapama
    $('#edit-account-btn').on('click', function() {
        loadAccountData();
        $('#account-modal').show();
    });

    $('.role-custom-modal-close, #cancel-account').on('click', function() {
        $('#account-modal').hide();
    });

    // Modal dışına tıklayınca kapat
    $('#account-modal').on('click', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });

    // Hesap bilgilerini kaydet
    $('#save-account').on('click', function() {
        saveAccountData();
    });

    // Para çekme formu gönderimi
    $('#withdraw-form').on('submit', function(e) {
        e.preventDefault();
        submitWithdrawRequest();
    });

    // IBAN formatı
    $(document).on('input', '#iban', function() {
        let value = $(this).val().replace(/\s/g, '').toUpperCase();
        let formatted = value.replace(/(.{4})/g, '$1 ').trim();
        $(this).val(formatted);
    });

    /**
     * Hesap verilerini yükle
     */
    function loadAccountData() {
        // Mevcut hesap bilgilerini modal'a yükle
        const bankName = $('.account-info p:contains("Banka Adı:")').text().replace('Banka Adı: ', '');
        const accountHolder = $('.account-info p:contains("Hesap Sahibi:")').text().replace('Hesap Sahibi: ', '');
        const iban = $('.account-info p:contains("IBAN:")').text().replace('IBAN: ', '');

        $('#bank-name').val(bankName);
        $('#account-holder').val(accountHolder);
        $('#iban').val(iban);
    }

    /**
     * Hesap verilerini kaydet
     */
    function saveAccountData() {
        const formData = {
            action: 'role_custom_save_withdraw_account',
            nonce: roleCustomWithdraw.nonce,
            bank_name: $('#bank-name').val(),
            account_holder: $('#account-holder').val(),
            iban: $('#iban').val(),
            account_number: $('#account-number').val()
        };

        // Validasyon
        if (!formData.bank_name || !formData.account_holder || !formData.iban) {
            showNotice('Lütfen zorunlu alanları doldurun.', 'error');
            return;
        }

        // IBAN validasyonu (basit)
        const ibanRegex = /^TR\d{2}\s?\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\s?\d{2}$/;
        if (!ibanRegex.test(formData.iban.replace(/\s/g, ''))) {
            showNotice('Geçerli bir IBAN girin.', 'error');
            return;
        }

        $('#save-account').addClass('loading').prop('disabled', true);

        $.ajax({
            url: roleCustomWithdraw.ajaxUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    showNotice(roleCustomWithdraw.strings.success, 'success');
                    $('#account-modal').hide();
                    location.reload(); // Sayfayı yenile
                } else {
                    showNotice(response.data || roleCustomWithdraw.strings.error, 'error');
                }
            },
            error: function() {
                showNotice(roleCustomWithdraw.strings.error, 'error');
            },
            complete: function() {
                $('#save-account').removeClass('loading').prop('disabled', false);
            }
        });
    }

    /**
     * Para çekme talebini gönder
     */
    function submitWithdrawRequest() {
        const amount = parseFloat($('#withdraw-amount').val());
        const note = $('#withdraw-note').val();
        const maxAmount = parseFloat($('#withdraw-amount').attr('max'));

        // Validasyon
        if (!amount || amount <= 0) {
            showNotice(roleCustomWithdraw.strings.amount_required, 'error');
            return;
        }

        // Minimum miktar kontrolü
        const minimumAmount = 50;
        if (amount < minimumAmount) {
            showNotice('Minimum para çekme miktarı ' + minimumAmount + ' TL olmalıdır.', 'error');
            return;
        }

        if (amount > maxAmount) {
            showNotice(roleCustomWithdraw.strings.amount_exceeds, 'error');
            return;
        }

        // Hesap bilgisi kontrolü
        if ($('.no-account').length > 0) {
            showNotice(roleCustomWithdraw.strings.account_required, 'error');
            return;
        }

        if (!confirm(roleCustomWithdraw.strings.confirm)) {
            return;
        }

        const formData = {
            action: 'role_custom_withdraw_request',
            nonce: roleCustomWithdraw.nonce,
            amount: amount,
            note: note
        };

        $('#withdraw-form button[type="submit"]').addClass('loading').prop('disabled', true);

        $.ajax({
            url: roleCustomWithdraw.ajaxUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    showNotice('Para çekme talebiniz başarıyla gönderildi!', 'success');
                    $('#withdraw-form')[0].reset();
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    showNotice(response.data || roleCustomWithdraw.strings.error, 'error');
                }
            },
            error: function() {
                showNotice(roleCustomWithdraw.strings.error, 'error');
            },
            complete: function() {
                $('#withdraw-form button[type="submit"]').removeClass('loading').prop('disabled', false);
            }
        });
    }

    /**
     * Bildirim göster
     */
    function showNotice(message, type) {
        // Mevcut bildirimleri kaldır
        $('.role-custom-notice').remove();

        const notice = $('<div class="role-custom-notice ' + type + '">' + message + '</div>');
        $('.role-custom-withdraw-page h1').after(notice);

        // 5 saniye sonra otomatik kaldır
        setTimeout(function() {
            notice.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    /**
     * Bakiye bilgilerini güncelle
     */
    function updateBalance() {
        $.ajax({
            url: roleCustomWithdraw.ajaxUrl,
            type: 'POST',
            data: {
                action: 'role_custom_get_withdraw_balance',
                nonce: roleCustomWithdraw.nonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    $('.balance-amount.total').text(data.total_earnings);
                    $('.balance-amount.withdrawn').text(data.total_withdrawn);
                    $('.balance-amount.available').text(data.available_balance);
                    $('#withdraw-amount').attr('max', data.available_balance_raw);
                }
            }
        });
    }

    // Sayfa yüklendiğinde bakiye güncelle
    updateBalance();
});
