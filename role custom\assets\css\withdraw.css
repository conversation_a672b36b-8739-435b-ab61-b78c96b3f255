/* Role Custom Para Çek<PERSON> */

.role-custom-withdraw-page {
    max-width: 1200px;
}

.role-custom-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.role-custom-card h2,
.role-custom-card h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #1d2327;
}

/* <PERSON><PERSON><PERSON> */
.role-custom-balance-card {
    background: linear-gradient(135deg, #2271b1 0%, #135e96 100%);
    color: white;
    border: none;
}

.role-custom-balance-card h2 {
    color: white;
    margin-bottom: 20px;
}

.balance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.balance-item {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.balance-label {
    display: block;
    font-size: 14px;
    margin-bottom: 8px;
    opacity: 0.9;
}

.balance-amount {
    display: block;
    font-size: 24px;
    font-weight: bold;
}

.balance-amount.total {
    color: #90ee90;
}

.balance-amount.withdrawn {
    color: #ffb6c1;
}

.balance-amount.available {
    color: #87ceeb;
}

/* Ana İçerik Alanı */
.role-custom-withdraw-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

@media (max-width: 768px) {
    .role-custom-withdraw-content {
        grid-template-columns: 1fr;
    }
}

/* Hesap Bilgileri */
.account-info p {
    margin: 8px 0;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f1;
}

.account-info p:last-child {
    border-bottom: none;
}

.no-account {
    color: #d63638;
    font-style: italic;
    margin-bottom: 15px;
}

/* Form Stilleri */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #1d2327;
}

.form-group input,
.form-group textarea {
    width: 100%;
    max-width: 300px;
}

.form-group .description {
    margin-top: 5px;
    color: #646970;
    font-size: 13px;
}

.no-balance {
    color: #d63638;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Modal Stilleri */
.role-custom-modal {
    display: none;
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.role-custom-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border-radius: 4px;
    width: 90%;
    max-width: 500px;
    position: relative;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.role-custom-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.role-custom-modal-header h3 {
    margin: 0;
    color: #1d2327;
}

.role-custom-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.role-custom-modal-close:hover {
    color: #d63638;
}

.role-custom-modal-body {
    margin-bottom: 20px;
}

.role-custom-modal-footer {
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

.role-custom-modal-footer .button {
    margin-left: 10px;
}

/* Para Çekme Geçmişi */
.withdraw-history {
    margin-top: 30px;
}

.withdraw-history table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.withdraw-history th,
.withdraw-history td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.withdraw-history th {
    background-color: #f9f9f9;
    font-weight: 600;
}

.withdraw-status {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.withdraw-status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.withdraw-status.approved {
    background-color: #d1edff;
    color: #0c5460;
}

.withdraw-status.rejected {
    background-color: #f8d7da;
    color: #721c24;
}

/* Loading Durumu */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 600px) {
    .role-custom-modal-content {
        margin: 10% auto;
        width: 95%;
        padding: 15px;
    }
    
    .balance-grid {
        grid-template-columns: 1fr;
    }
    
    .balance-item {
        padding: 10px;
    }
    
    .balance-amount {
        font-size: 20px;
    }
}

/* Başarı ve Hata Mesajları */
.role-custom-notice {
    padding: 12px;
    margin: 15px 0;
    border-radius: 4px;
    border-left: 4px solid;
}

.role-custom-notice.success {
    background-color: #d1edff;
    border-left-color: #00a32a;
    color: #00a32a;
}

.role-custom-notice.error {
    background-color: #f8d7da;
    border-left-color: #d63638;
    color: #d63638;
}

.role-custom-notice.warning {
    background-color: #fff3cd;
    border-left-color: #dba617;
    color: #856404;
}
