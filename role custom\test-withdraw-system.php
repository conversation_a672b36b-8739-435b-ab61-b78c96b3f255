<?php
/**
 * Role Custom Para Çekme Sistemi Test Dosyası
 * Bu dosya sadece test amaçlıdır, production'da silinmelidir.
 */

// WordPress'i yükle
if (!defined('ABSPATH')) {
    require_once '../../../wp-config.php';
    require_once '../../../wp-load.php';
}

// Sadece admin kullanıcılar erişebilir
if (!current_user_can('manage_options')) {
    wp_die('Bu sayfaya erişim yetkiniz yok.');
}

global $wpdb;

echo '<h1>Role Custom Para Çekme Sistemi Test</h1>';

// 1. Veritabanı tablolarını kontrol et
echo '<h2>1. Veritabanı Tabloları</h2>';
$withdraw_table = $wpdb->prefix . 'role_custom_withdraws';
$accounts_table = $wpdb->prefix . 'role_custom_withdraw_accounts';

$withdraw_exists = $wpdb->get_var("SHOW TABLES LIKE '$withdraw_table'");
$accounts_exists = $wpdb->get_var("SHOW TABLES LIKE '$accounts_table'");

echo '<p>Withdraw tablosu: ' . ($withdraw_exists ? '<span style="color:green">✓ Mevcut</span>' : '<span style="color:red">✗ Yok</span>') . '</p>';
echo '<p>Accounts tablosu: ' . ($accounts_exists ? '<span style="color:green">✓ Mevcut</span>' : '<span style="color:red">✗ Yok</span>') . '</p>';

// 2. Eklenti durumunu kontrol et
echo '<h2>2. Eklenti Durumu</h2>';
if (function_exists('is_plugin_active')) {
    $plugin_active = is_plugin_active('role custom/role-custom.php');
    echo '<p>Role Custom eklentisi: ' . ($plugin_active ? '<span style="color:green">✓ Aktif</span>' : '<span style="color:red">✗ Aktif değil</span>') . '</p>';
}

// 3. Veritabanı versiyonu
echo '<h2>3. Veritabanı Versiyonu</h2>';
$db_version = get_option('role_custom_db_version', '0');
echo '<p>Veritabanı versiyonu: ' . $db_version . '</p>';

// 4. Tutor LMS kontrolü
echo '<h2>4. Tutor LMS Kontrolü</h2>';
if (function_exists('tutor')) {
    echo '<p>Tutor LMS: <span style="color:green">✓ Aktif</span></p>';
    
    // Tutor earnings tablosunu kontrol et
    $tutor_earnings_table = $wpdb->prefix . 'tutor_earnings';
    $tutor_earnings_exists = $wpdb->get_var("SHOW TABLES LIKE '$tutor_earnings_table'");
    echo '<p>Tutor earnings tablosu: ' . ($tutor_earnings_exists ? '<span style="color:green">✓ Mevcut</span>' : '<span style="color:red">✗ Yok</span>') . '</p>';
} else {
    echo '<p>Tutor LMS: <span style="color:red">✗ Aktif değil</span></p>';
}

// 5. WooCommerce kontrolü
echo '<h2>5. WooCommerce Kontrolü</h2>';
if (function_exists('wc_price')) {
    echo '<p>WooCommerce: <span style="color:green">✓ Aktif</span></p>';
    echo '<p>Test fiyat formatı: ' . wc_price(1234.56) . '</p>';
} else {
    echo '<p>WooCommerce: <span style="color:red">✗ Aktif değil</span></p>';
}

// 6. Kullanıcı rolleri kontrolü
echo '<h2>6. Kullanıcı Rolleri</h2>';
$users = get_users(['role' => 'tutor_instructor', 'number' => 5]);
echo '<p>Tutor Instructor kullanıcı sayısı: ' . count($users) . '</p>';

if (!empty($users)) {
    echo '<h3>Örnek Eğitmenler:</h3>';
    foreach ($users as $user) {
        echo '<p>- ' . $user->display_name . ' (' . $user->user_email . ')</p>';
    }
}

// 7. AJAX endpoint'leri kontrolü
echo '<h2>7. AJAX Endpoint\'leri</h2>';
$ajax_actions = [
    'role_custom_withdraw_request',
    'role_custom_get_withdraw_balance',
    'role_custom_save_withdraw_account',
    'role_custom_approve_withdraw',
    'role_custom_reject_withdraw'
];

foreach ($ajax_actions as $action) {
    $hook_exists = has_action("wp_ajax_$action");
    echo '<p>' . $action . ': ' . ($hook_exists ? '<span style="color:green">✓ Kayıtlı</span>' : '<span style="color:red">✗ Kayıtlı değil</span>') . '</p>';
}

// 8. Asset dosyaları kontrolü
echo '<h2>8. Asset Dosyaları</h2>';
$assets = [
    'assets/css/withdraw.css',
    'assets/js/withdraw.js',
    'assets/css/admin-withdraw.css',
    'assets/js/admin-withdraw.js'
];

foreach ($assets as $asset) {
    $file_path = plugin_dir_path(__FILE__) . $asset;
    $exists = file_exists($file_path);
    echo '<p>' . $asset . ': ' . ($exists ? '<span style="color:green">✓ Mevcut</span>' : '<span style="color:red">✗ Yok</span>') . '</p>';
}

// 9. Test verileri oluştur (sadece test için)
if (isset($_GET['create_test_data']) && $_GET['create_test_data'] === '1') {
    echo '<h2>9. Test Verileri Oluşturuluyor...</h2>';
    
    // Test kullanıcısı bul
    $test_user = get_users(['role' => 'tutor_instructor', 'number' => 1]);
    
    if (!empty($test_user)) {
        $user_id = $test_user[0]->ID;
        
        // Test hesap bilgisi ekle
        $account_data = [
            'bank_name' => 'Test Bankası',
            'account_holder' => 'Test Kullanıcı',
            'iban' => 'TR12 3456 7890 1234 5678 9012 34',
            'account_number' => '**********'
        ];
        
        $wpdb->replace(
            $wpdb->prefix . 'role_custom_withdraw_accounts',
            [
                'user_id' => $user_id,
                'method' => 'bank',
                'account_data' => json_encode($account_data),
                'is_default' => 1
            ]
        );
        
        // Test para çekme talebi ekle
        $wpdb->insert(
            $wpdb->prefix . 'role_custom_withdraws',
            [
                'user_id' => $user_id,
                'amount' => 150.00,
                'status' => 0, // Pending
                'method' => 'bank',
                'note' => 'Test para çekme talebi',
                'details' => json_encode($account_data),
                'ip' => '127.0.0.1'
            ]
        );
        
        echo '<p><span style="color:green">✓ Test verileri oluşturuldu!</span></p>';
    } else {
        echo '<p><span style="color:red">✗ Test için tutor_instructor kullanıcısı bulunamadı!</span></p>';
    }
}

echo '<hr>';
echo '<p><a href="?create_test_data=1" style="background:#0073aa;color:white;padding:10px;text-decoration:none;border-radius:3px;">Test Verileri Oluştur</a></p>';
echo '<p><small>Bu test dosyasını production ortamında silmeyi unutmayın!</small></p>';
?>
